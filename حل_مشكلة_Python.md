# 🔧 حل مشكلة Python - Microsoft Store

## ❌ المشكلة:
```
Python was not found; run without arguments to install from the Microsoft Store
```

## 🎯 السبب:
Windows يحول أمر `python` إلى Microsoft Store بدلاً من Python الحقيقي المثبت.

## ✅ الحلول السريعة:

### الحل 1 (الأسرع): استخدم `py` بدلاً من `python`
**انقر مزدوج على:** `run_py.bat`

### الحل 2 (الأذكى): البحث التلقائي
**انقر مزدوج على:** `find_and_run.bat`

### الحل 3 (الشامل): جرب كل الطرق
**انقر مزدوج على:** `run_fixed.bat`

## 🛠️ الحل النهائي (إعدادات Windows):

### خطوات إلغاء Microsoft Store Python:
1. **اضغط Windows + I**
2. **اذهب إلى:** Apps
3. **اضغط على:** Advanced app settings
4. **اضغط على:** App execution aliases
5. **أغلق (OFF):** 
   - App Installer - python.exe
   - App Installer - python3.exe

### بعد ذلك:
**انقر مزدوج على:** `run.bat` (سيعمل الآن!)

## 🔍 للتحقق من Python:

### افتح Command Prompt واكتب:
```cmd
py --version
```
أو
```cmd
python --version
```

### إذا ظهر رقم الإصدار → Python يعمل ✅

## 📋 ملفات التشغيل الجديدة:

### 1. **`run_py.bat`** (الأسرع)
- **يستخدم `py`** بدلاً من `python`
- **يتجنب مشكلة Microsoft Store**
- **يعمل فوراً**

### 2. **`find_and_run.bat`** (الأذكى)
- **يبحث عن Python** تلقائياً
- **يجرب مسارات مختلفة**
- **يجد Python حتى لو مخفي**

### 3. **`run_fixed.bat`** (الشامل)
- **يجرب كل الطرق**
- **رسائل مفصلة**
- **حلول للمشاكل**

## 💡 نصائح:

### للاستخدام السريع:
1. **جرب `run_py.bat` أولاً**
2. **إذا لم يعمل** → جرب `find_and_run.bat`
3. **للحل النهائي** → غير إعدادات Windows

### لتجنب المشكلة مستقبلاً:
- **أغلق Python aliases** في Windows
- **أو استخدم `py`** دائماً بدلاً من `python`

## 🎯 الخلاصة:

**أسرع حل:**
```
انقر مزدوج على: run_py.bat
```

**أفضل حل:**
```
1. غير إعدادات Windows (كما هو موضح أعلاه)
2. ثم استخدم run.bat
```

**هذا سيحل المشكلة نهائياً!** ✅
