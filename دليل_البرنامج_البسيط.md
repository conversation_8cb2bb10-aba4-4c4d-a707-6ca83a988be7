# منظف ملفات SRT البسيط

## 🎯 الوظائف فقط

### ما يحذفه البرنامج:
- ✅ **التوقيتات** (00:00:01,000 --> 00:00:05,000)
- ✅ **أرقام الترقيم** (1, 2, 3, 4...)
- ✅ **كلمة "كورس"** (بالعربية والإنجليزية)

### ما يحافظ عليه:
- ✅ **النص الأصلي** كما هو تماماً
- ✅ **التشكيل** والرموز
- ✅ **الكلمات الإنجليزية** الأخرى
- ✅ **ترتيب الكلمات** الطبيعي

## 🚀 طريقة الاستخدام

### خطوات بسيطة:
1. **شغل البرنامج**: انقر على `تشغيل_المنظف_البسيط.bat`
2. **اختر الملف**: اضغط "اختر ملف" واختر ملف SRT
3. **نظف الملف**: اضغط "تنظيف الملف"
4. **شاهد النتيجة**: في المنطقة البيضاء
5. **احفظ النص**: اضغط "حفظ النص"

## 📋 الأزرار الأربعة

### 🔵 تنظيف الملف
- يقرأ ملف SRT
- يحذف التوقيت والترقيم وكلمة "كورس"
- يعرض النتيجة

### 🟠 حفظ نص
- يحفظ النص المنظف كملف .txt
- بترميز UTF-8 للعربية

### 🟣 حفظ PDF
- يحفظ النص المنظف كملف .pdf
- تنسيق جميل مع خط كبير
- يظهر فقط إذا كانت مكتبة reportlab مثبتة

### 🔴 مسح
- يمسح كل شيء للبدء من جديد

## 🔧 تثبيت مكتبة PDF

### إذا لم يظهر زر "حفظ PDF":
- سيظهر زر "تثبيت PDF" بدلاً منه
- اضغط عليه لتثبيت المكتبة تلقائياً
- أو ثبت يدوياً: `pip install reportlab`

## 🧪 مثال

### قبل التنظيف:
```
1
00:00:01,000 --> 00:00:05,000
مرحباً بكم في كورس البرمجة

2
00:00:05,500 --> 00:00:10,000
سنتعلم اليوم أساسيات البرمجة
```

### بعد التنظيف:
```
مرحباً بكم في البرمجة
سنتعلم اليوم أساسيات البرمجة
```

## ✨ المميزات

- **بساطة**: 3 أزرار فقط
- **سرعة**: تنظيف فوري
- **أمان**: لا يغير النص الأصلي
- **وضوح**: خط كبير للقراءة
- **دعم عربي**: ترميز UTF-8

## 📁 الملفات

- `srt_simple_cleaner.py` - البرنامج الرئيسي
- `تشغيل_المنظف_البسيط.bat` - تشغيل سريع
- ملفات SRT للاختبار

## 🔧 متطلبات

- Python مثبت على الجهاز
- لا حاجة لمكتبات إضافية
- يعمل على Windows/Mac/Linux

## ❓ استكشاف الأخطاء

### إذا لم يعمل البرنامج:
1. تأكد من تثبيت Python
2. شغل من Command Prompt: `python srt_simple_cleaner.py`

### إذا ظهر خطأ في قراءة الملف:
1. تأكد أن الملف SRT صحيح
2. جرب ملف آخر

### إذا لم يحفظ الملف:
1. تأكد من صلاحيات الكتابة
2. اختر مجلد آخر للحفظ
