#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منظف ملفات SRT النهائي - بسيط وفعال
يحذف: التوقيت + الترقيم + كلمة "كورس"
يحفظ: نص + HTML (للطباعة كـ PDF)
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re
import os
import webbrowser


class FinalSRTCleaner:
    def __init__(self, root):
        self.root = root
        self.root.title("منظف SRT النهائي")
        self.root.geometry("650x550")
        self.root.configure(bg='#f5f5f5')

        # متغيرات
        self.input_file = ""
        self.cleaned_text = ""

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.root,
            text="🧹 منظف ملفات SRT",
            font=("Arial", 20, "bold"),
            bg='#f5f5f5',
            fg='#1976D2'
        )
        title_label.pack(pady=15)

        # وصف الوظائف
        desc_label = tk.Label(
            self.root,
            text="يحذف: التوقيت + الترقيم + كلمة 'كورس' | يحفظ: نص + PDF",
            font=("Arial", 11),
            bg='#f5f5f5',
            fg='#666666'
        )
        desc_label.pack(pady=(0, 15))

        # إطار اختيار الملف
        file_frame = tk.LabelFrame(
            self.root,
            text="📁 اختيار الملف",
            font=("Arial", 12, "bold"),
            bg='#f5f5f5',
            fg='#333333'
        )
        file_frame.pack(pady=10, padx=20, fill='x')

        file_select_frame = tk.Frame(file_frame, bg='#f5f5f5')
        file_select_frame.pack(fill='x', padx=10, pady=10)

        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            file_select_frame,
            textvariable=self.file_path_var,
            font=("Arial", 11),
            state='readonly',
            bg='white'
        )
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(
            file_select_frame,
            text="📂 تصفح",
            command=self.browse_file,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=8,
            relief='flat'
        )
        browse_btn.pack(side='right')

        # منطقة معاينة النص
        preview_frame = tk.LabelFrame(
            self.root,
            text="📄 النص المنظف",
            font=("Arial", 12, "bold"),
            bg='#f5f5f5',
            fg='#333333'
        )
        preview_frame.pack(pady=15, padx=20, fill='both', expand=True)

        # منطقة النص مع scrollbar
        self.text_preview = scrolledtext.ScrolledText(
            preview_frame,
            height=12,
            font=("Arial", 14),  # خط كبير
            wrap='word',
            bg='white',
            fg='#333333',
            relief='flat',
            borderwidth=1
        )
        self.text_preview.pack(fill='both', expand=True, padx=10, pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#f5f5f5')
        buttons_frame.pack(pady=20, padx=20, fill='x')

        # زر التنظيف
        clean_btn = tk.Button(
            buttons_frame,
            text="🧹 تنظيف",
            command=self.clean_file,
            bg='#2196F3',
            fg='white',
            font=("Arial", 13, "bold"),
            padx=25,
            pady=10,
            relief='flat'
        )
        clean_btn.pack(side='left', padx=(0, 10))

        # زر حفظ نص
        save_text_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ تلقائي",
            command=self.save_text,
            bg='#FF9800',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=10,
            relief='flat'
        )
        save_text_btn.pack(side='left', padx=(0, 10))

        # زر حفظ PDF
        save_pdf_btn = tk.Button(
            buttons_frame,
            text="📄 PDF",
            command=self.save_as_pdf,
            bg='#E91E63',
            fg='white',
            font=("Arial", 13, "bold"),
            padx=25,
            pady=10,
            relief='flat'
        )
        save_pdf_btn.pack(side='left', padx=(0, 10))

        # زر المسح
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ مسح",
            command=self.clear_all,
            bg='#757575',
            fg='white',
            font=("Arial", 13, "bold"),
            padx=25,
            pady=10,
            relief='flat'
        )
        clear_btn.pack(side='right')

        # شريط الحالة
        self.status_label = tk.Label(
            self.root,
            text="جاهز للاستخدام",
            font=("Arial", 10),
            bg='#f5f5f5',
            fg='#666666'
        )
        self.status_label.pack(pady=(10, 5))

    def browse_file(self):
        """تصفح واختيار ملف SRT"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف SRT",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            self.input_file = file_path
            self.file_path_var.set(file_path)
            self.status_label.config(text=f"تم اختيار: {os.path.basename(file_path)}")

    def read_file(self, file_path):
        """قراءة ملف SRT"""
        try:
            encodings = ['utf-8', 'utf-8-sig', 'cp1256', 'windows-1256', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        return content
                except UnicodeDecodeError:
                    continue

            raise Exception("لا يمكن قراءة الملف بأي ترميز مدعوم")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف: {str(e)}")
            return None

    def clean_srt_content(self, content):
        """تنظيف محتوى ملف SRT"""
        if not content:
            return ""

        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # تخطي الأسطر الفارغة
            if not line:
                continue

            # حذف أرقام الترقيم
            if line.isdigit():
                continue

            # حذف التوقيتات
            if '-->' in line:
                continue

            # حذف كلمة "كورس"
            line = re.sub(r'\bكورس\b', '', line, flags=re.IGNORECASE)
            line = re.sub(r'\bcourse\b', '', line, flags=re.IGNORECASE)

            # تنظيف المسافات
            line = re.sub(r'\s+', ' ', line).strip()

            if line:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def clean_file(self):
        """تنظيف الملف المحدد"""
        if not self.input_file:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف SRT أولاً")
            return

        self.status_label.config(text="جاري التنظيف...")
        self.root.update()

        try:
            content = self.read_file(self.input_file)
            if content is None:
                return

            self.cleaned_text = self.clean_srt_content(content)

            # عرض النتيجة
            self.text_preview.delete(1.0, tk.END)
            self.text_preview.insert(1.0, self.cleaned_text)

            # إحصائيات
            lines_count = len([line for line in self.cleaned_text.split('\n') if line.strip()])
            words_count = len(self.cleaned_text.split())

            self.status_label.config(text=f"تم! {lines_count} سطر، {words_count} كلمة")

            messagebox.showinfo(
                "تم التنظيف!",
                f"✅ تم تنظيف الملف بنجاح!\n\n"
                f"📊 النتائج:\n"
                f"• الأسطر: {lines_count}\n"
                f"• الكلمات: {words_count}\n"
                f"• الأحرف: {len(self.cleaned_text)}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنظيف الملف: {str(e)}")
            self.status_label.config(text="فشل في التنظيف")

    def save_text(self):
        """حفظ النص كملف .txt تلقائياً بنفس اسم الملف الأصلي + رقم 2"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        if not self.input_file:
            messagebox.showwarning("تحذير", "لا يوجد ملف أصلي محدد")
            return

        try:
            # إنشاء اسم الملف الجديد تلقائياً
            original_dir = os.path.dirname(self.input_file)
            original_name = os.path.splitext(os.path.basename(self.input_file))[0]
            new_filename = f"{original_name}_2.txt"
            output_path = os.path.join(original_dir, new_filename)

            # التحقق من وجود الملف وإضافة رقم إضافي إذا لزم الأمر
            counter = 2
            while os.path.exists(output_path):
                counter += 1
                new_filename = f"{original_name}_{counter}.txt"
                output_path = os.path.join(original_dir, new_filename)

            # حفظ النص بدون عنوان
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(self.cleaned_text)

            self.status_label.config(text=f"تم حفظ: {new_filename}")
            messagebox.showinfo("تم الحفظ!", f"✅ تم حفظ النص تلقائياً:\n📁 {new_filename}\n📍 في نفس مجلد الملف الأصلي")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def save_as_pdf(self):
        """حفظ النص كـ PDF (عبر HTML)"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        # إنشاء ملف HTML مؤقت
        try:
            import tempfile

            # إنشاء محتوى HTML بدون عنوان
            html_content = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>النص المنظف</title>
    <style>
        body {{
            font-family: 'Tahoma', 'Arial', sans-serif;
            font-size: 18px;
            line-height: 2;
            margin: 30px;
            direction: rtl;
            text-align: right;
            color: #333;
        }}

        .content {{
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }}

        p {{
            margin-bottom: 20px;
            text-align: right;
            text-indent: 20px;
        }}

        .instructions {{
            background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid #2196F3;
        }}

        .instructions strong {{
            color: #1976D2;
            font-size: 20px;
        }}

        @media print {{
            .instructions {{
                display: none;
            }}
            body {{
                margin: 15px;
            }}
        }}
    </style>
</head>
<body>
    <div class="content">
        <div class="instructions">
            <strong>📋 تعليمات التحويل إلى PDF:</strong><br>
            اضغط <strong>Ctrl + P</strong> ← اختر <strong>"حفظ كـ PDF"</strong> ← احفظ
        </div>

"""

            # إضافة النص
            lines = self.cleaned_text.split('\n')
            for line in lines:
                if line.strip():
                    html_content += f"        <p>{line.strip()}</p>\n"

            html_content += """    </div>
</body>
</html>"""

            # حفظ ملف HTML مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(html_content)
                temp_path = temp_file.name

            # فتح في المتصفح
            webbrowser.open('file://' + temp_path)

            self.status_label.config(text="تم فتح HTML للتحويل إلى PDF")

            messagebox.showinfo(
                "📄 جاهز للتحويل!",
                "✅ تم فتح الملف في المتصفح!\n\n"
                "📋 للحصول على PDF:\n"
                "1️⃣ اضغط Ctrl + P\n"
                "2️⃣ اختر 'حفظ كـ PDF'\n"
                "3️⃣ احفظ في المكان المطلوب\n\n"
                "💡 النتيجة ستكون ملف PDF جميل ومنسق!"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء HTML: {str(e)}")

    def clear_all(self):
        """مسح كل شيء"""
        self.text_preview.delete(1.0, tk.END)
        self.cleaned_text = ""
        self.file_path_var.set("")
        self.input_file = ""
        self.status_label.config(text="تم المسح - جاهز للاستخدام")


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = FinalSRTCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()
