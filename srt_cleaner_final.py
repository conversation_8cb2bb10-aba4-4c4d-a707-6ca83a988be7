#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منظف ملفات SRT النهائي - بسيط وفعال
يحذف: التوقيت + الترقيم + كلمة "كورس"
يحافظ على: التنسيق الأصلي + المحاذاة حسب اللغة
يحفظ: نص تلقائي باسم الملف الأصلي + _2
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re
import os


class FinalSRTCleaner:
    def __init__(self, root):
        self.root = root
        self.root.title("منظف SRT النهائي")
        self.root.geometry("650x550")
        self.root.configure(bg='#f5f5f5')

        # متغيرات
        self.input_file = ""
        self.cleaned_text = ""

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.root,
            text="🧹 منظف ملفات SRT",
            font=("Arial", 20, "bold"),
            bg='#f5f5f5',
            fg='#1976D2'
        )
        title_label.pack(pady=15)

        # وصف الوظائف
        desc_label = tk.Label(
            self.root,
            text="يحذف: التوقيت + الترقيم + كلمة 'كورس' | يحافظ على: التنسيق + المحاذاة",
            font=("Arial", 11),
            bg='#f5f5f5',
            fg='#666666'
        )
        desc_label.pack(pady=(0, 15))

        # إطار اختيار الملف
        file_frame = tk.LabelFrame(
            self.root,
            text="📁 اختيار الملف",
            font=("Arial", 12, "bold"),
            bg='#f5f5f5',
            fg='#333333'
        )
        file_frame.pack(pady=10, padx=20, fill='x')

        file_select_frame = tk.Frame(file_frame, bg='#f5f5f5')
        file_select_frame.pack(fill='x', padx=10, pady=10)

        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            file_select_frame,
            textvariable=self.file_path_var,
            font=("Arial", 11),
            state='readonly',
            bg='white'
        )
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(
            file_select_frame,
            text="📂 تصفح",
            command=self.browse_file,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=8,
            relief='flat'
        )
        browse_btn.pack(side='right')

        # منطقة معاينة النص
        preview_frame = tk.LabelFrame(
            self.root,
            text="📄 النص المنظف",
            font=("Arial", 12, "bold"),
            bg='#f5f5f5',
            fg='#333333'
        )
        preview_frame.pack(pady=15, padx=20, fill='both', expand=True)

        # منطقة النص مع scrollbar
        self.text_preview = scrolledtext.ScrolledText(
            preview_frame,
            height=12,
            font=("Arial", 14),  # خط كبير
            wrap='word',
            bg='white',
            fg='#333333',
            relief='flat',
            borderwidth=1
        )
        self.text_preview.pack(fill='both', expand=True, padx=10, pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#f5f5f5')
        buttons_frame.pack(pady=20, padx=20, fill='x')

        # زر التنظيف
        clean_btn = tk.Button(
            buttons_frame,
            text="🧹 تنظيف",
            command=self.clean_file,
            bg='#2196F3',
            fg='white',
            font=("Arial", 13, "bold"),
            padx=25,
            pady=10,
            relief='flat'
        )
        clean_btn.pack(side='left', padx=(0, 10))

        # زر حفظ نص
        save_text_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ تلقائي",
            command=self.save_text,
            bg='#FF9800',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=10,
            relief='flat'
        )
        save_text_btn.pack(side='left', padx=(0, 10))



        # زر المسح
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ مسح",
            command=self.clear_all,
            bg='#757575',
            fg='white',
            font=("Arial", 13, "bold"),
            padx=25,
            pady=10,
            relief='flat'
        )
        clear_btn.pack(side='right')

        # شريط الحالة
        self.status_label = tk.Label(
            self.root,
            text="جاهز للاستخدام",
            font=("Arial", 10),
            bg='#f5f5f5',
            fg='#666666'
        )
        self.status_label.pack(pady=(10, 5))

    def browse_file(self):
        """تصفح واختيار ملف SRT"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف SRT",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            self.input_file = file_path
            self.file_path_var.set(file_path)
            self.status_label.config(text=f"تم اختيار: {os.path.basename(file_path)}")

    def read_file(self, file_path):
        """قراءة ملف SRT"""
        try:
            encodings = ['utf-8', 'utf-8-sig', 'cp1256', 'windows-1256', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        return content
                except UnicodeDecodeError:
                    continue

            raise Exception("لا يمكن قراءة الملف بأي ترميز مدعوم")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف: {str(e)}")
            return None

    def clean_srt_content(self, content):
        """تنظيف محتوى ملف SRT مع الحفاظ على التنسيق الأصلي"""
        if not content:
            return ""

        lines = content.split('\n')
        cleaned_lines = []
        previous_was_number = False

        for line in lines:
            original_line = line
            line = line.strip()

            # إذا كان السطر فارغ، نحافظ عليه
            if not line:
                cleaned_lines.append("")
                continue

            # إذا كان رقم ترقيم، نضع سطر فارغ بدلاً منه
            if line.isdigit():
                if not previous_was_number:  # تجنب الأسطر الفارغة المتتالية
                    cleaned_lines.append("")
                previous_was_number = True
                continue
            else:
                previous_was_number = False

            # حذف التوقيتات
            if '-->' in line:
                continue

            # حذف كلمة "كورس" مع الحفاظ على باقي النص
            line = re.sub(r'\bكورس\b', '', line, flags=re.IGNORECASE)
            line = re.sub(r'\bcourse\b', '', line, flags=re.IGNORECASE)

            # تنظيف المسافات الزائدة فقط (بدون تغيير التنسيق)
            line = re.sub(r'\s+', ' ', line).strip()

            if line:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def is_arabic_text(self, text):
        """تحديد ما إذا كان النص عربي أم لا"""
        if not text.strip():
            return False

        arabic_chars = 0
        total_chars = 0

        for char in text:
            if char.isalpha():
                total_chars += 1
                if '\u0600' <= char <= '\u06FF':  # نطاق الأحرف العربية
                    arabic_chars += 1

        if total_chars == 0:
            return False

        # إذا كان أكثر من 50% من الأحرف عربية
        return (arabic_chars / total_chars) > 0.5

    def display_text_with_alignment(self, text):
        """عرض النص مع المحاذاة المناسبة حسب اللغة"""
        lines = text.split('\n')

        for i, line in enumerate(lines):
            line_start = f"{i+1}.0"
            line_end = f"{i+1}.end"

            # إدراج النص
            self.text_preview.insert(tk.END, line + '\n')

            # تحديد المحاذاة حسب اللغة
            if line.strip():  # فقط للأسطر غير الفارغة
                if self.is_arabic_text(line):
                    # محاذاة يمين للعربية
                    self.text_preview.tag_add(f"arabic_{i}", line_start, line_end)
                    self.text_preview.tag_config(f"arabic_{i}", justify='right')
                else:
                    # محاذاة يسار للإنجليزية
                    self.text_preview.tag_add(f"english_{i}", line_start, line_end)
                    self.text_preview.tag_config(f"english_{i}", justify='left')

    def clean_file(self):
        """تنظيف الملف المحدد"""
        if not self.input_file:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف SRT أولاً")
            return

        self.status_label.config(text="جاري التنظيف...")
        self.root.update()

        try:
            content = self.read_file(self.input_file)
            if content is None:
                return

            self.cleaned_text = self.clean_srt_content(content)

            # عرض النتيجة مع المحاذاة المناسبة
            self.text_preview.delete(1.0, tk.END)
            self.display_text_with_alignment(self.cleaned_text)

            # إحصائيات
            lines_count = len([line for line in self.cleaned_text.split('\n') if line.strip()])
            words_count = len(self.cleaned_text.split())

            self.status_label.config(text=f"تم! {lines_count} سطر، {words_count} كلمة")

            messagebox.showinfo(
                "تم التنظيف!",
                f"✅ تم تنظيف الملف بنجاح!\n\n"
                f"📊 النتائج:\n"
                f"• الأسطر: {lines_count}\n"
                f"• الكلمات: {words_count}\n"
                f"• الأحرف: {len(self.cleaned_text)}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنظيف الملف: {str(e)}")
            self.status_label.config(text="فشل في التنظيف")

    def save_text(self):
        """حفظ النص كملف .txt تلقائياً بنفس اسم الملف الأصلي + رقم 2"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        if not self.input_file:
            messagebox.showwarning("تحذير", "لا يوجد ملف أصلي محدد")
            return

        try:
            # إنشاء اسم الملف الجديد تلقائياً
            original_dir = os.path.dirname(self.input_file)
            original_name = os.path.splitext(os.path.basename(self.input_file))[0]
            new_filename = f"{original_name}_2.txt"
            output_path = os.path.join(original_dir, new_filename)

            # التحقق من وجود الملف وإضافة رقم إضافي إذا لزم الأمر
            counter = 2
            while os.path.exists(output_path):
                counter += 1
                new_filename = f"{original_name}_{counter}.txt"
                output_path = os.path.join(original_dir, new_filename)

            # حفظ النص بدون عنوان
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(self.cleaned_text)

            self.status_label.config(text=f"تم حفظ: {new_filename}")
            messagebox.showinfo("تم الحفظ!", f"✅ تم حفظ النص تلقائياً:\n📁 {new_filename}\n📍 في نفس مجلد الملف الأصلي")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def clear_all(self):
        """مسح كل شيء"""
        self.text_preview.delete(1.0, tk.END)
        self.cleaned_text = ""
        self.file_path_var.set("")
        self.input_file = ""
        self.status_label.config(text="تم المسح - جاهز للاستخدام")


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = FinalSRTCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()
