#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج ملفات SRT المبسط - بدون مكتبات خارجية
يقوم بحذف التوقيتات وكلمة "كورس" وحفظ النتيجة في ملف نصي
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re
import os

# محاولة استيراد مكتبات PDF
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
    import urllib.request
    import tempfile
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False


class SimpleSRTProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("معالج ملفات SRT - تحويل إلى PDF")
        self.root.geometry("700x600")
        self.root.configure(bg='#f0f0f0')

        # متغيرات
        self.input_file = ""
        self.processed_text = ""

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.root,
            text="معالج ملفات SRT - تحويل إلى PDF",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack(pady=20)

        # إطار اختيار الملف
        file_frame = tk.Frame(self.root, bg='#f0f0f0')
        file_frame.pack(pady=10, padx=20, fill='x')

        tk.Label(file_frame, text="ملف SRT:", font=("Arial", 12), bg='#f0f0f0').pack(anchor='w')

        file_select_frame = tk.Frame(file_frame, bg='#f0f0f0')
        file_select_frame.pack(fill='x', pady=5)

        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            file_select_frame,
            textvariable=self.file_path_var,
            font=("Arial", 10),
            state='readonly'
        )
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(
            file_select_frame,
            text="تصفح",
            command=self.browse_file,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 10),
            padx=20
        )
        browse_btn.pack(side='right')

        # إطار الخيارات
        options_frame = tk.LabelFrame(
            self.root,
            text="خيارات المعالجة",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#333333'
        )
        options_frame.pack(pady=20, padx=20, fill='x')

        self.remove_timing = tk.BooleanVar(value=True)
        self.remove_course = tk.BooleanVar(value=True)
        self.remove_numbers = tk.BooleanVar(value=True)
        self.remove_empty_lines = tk.BooleanVar(value=True)
        self.clean_arabic_text = tk.BooleanVar(value=True)
        self.remove_english_words = tk.BooleanVar(value=False)

        tk.Checkbutton(
            options_frame,
            text="حذف التوقيتات",
            variable=self.remove_timing,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        tk.Checkbutton(
            options_frame,
            text="حذف كلمة 'كورس'",
            variable=self.remove_course,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        tk.Checkbutton(
            options_frame,
            text="حذف أرقام الترقيم",
            variable=self.remove_numbers,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        tk.Checkbutton(
            options_frame,
            text="حذف الأسطر الفارغة",
            variable=self.remove_empty_lines,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        tk.Checkbutton(
            options_frame,
            text="تنظيف النص العربي (إزالة التشكيل والرموز الزائدة)",
            variable=self.clean_arabic_text,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        tk.Checkbutton(
            options_frame,
            text="حذف الكلمات الإنجليزية",
            variable=self.remove_english_words,
            font=("Arial", 10),
            bg='#f0f0f0'
        ).pack(anchor='w', padx=10, pady=5)

        # منطقة معاينة النص
        preview_frame = tk.LabelFrame(
            self.root,
            text="معاينة النص المعالج",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#333333'
        )
        preview_frame.pack(pady=10, padx=20, fill='both', expand=True)

        # منطقة النص مع scrollbar
        self.text_preview = scrolledtext.ScrolledText(
            preview_frame,
            height=15,
            font=("Arial", 15),  # زيادة حجم الخط من 10 إلى 15 (50% أكبر)
            wrap='word',
            bg='white',
            fg='#333333'
        )
        self.text_preview.pack(fill='both', expand=True, padx=10, pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(pady=20, padx=20, fill='x')

        process_btn = tk.Button(
            buttons_frame,
            text="معالجة الملف",
            command=self.process_file,
            bg='#2196F3',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=30,
            pady=10
        )
        process_btn.pack(side='left', padx=(0, 10))

        save_btn = tk.Button(
            buttons_frame,
            text="حفظ النص",
            command=self.save_text,
            bg='#FF9800',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=30,
            pady=10
        )
        save_btn.pack(side='left', padx=(0, 10))

        # زر حفظ PDF (يظهر فقط إذا كانت مكتبة reportlab متوفرة)
        if PDF_AVAILABLE:
            pdf_btn = tk.Button(
                buttons_frame,
                text="حفظ PDF",
                command=self.save_pdf,
                bg='#9C27B0',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=30,
                pady=10
            )
            pdf_btn.pack(side='left', padx=(0, 10))

        clear_btn = tk.Button(
            buttons_frame,
            text="مسح",
            command=self.clear_text,
            bg='#f44336',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=30,
            pady=10
        )
        clear_btn.pack(side='left')

    def browse_file(self):
        """تصفح واختيار ملف SRT"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف SRT",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            self.input_file = file_path
            self.file_path_var.set(file_path)

    def read_srt_file(self, file_path):
        """قراءة ملف SRT"""
        try:
            # محاولة قراءة الملف بترميزات مختلفة
            encodings = ['utf-8', 'utf-8-sig', 'cp1256', 'iso-8859-1', 'windows-1256']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        return content
                except UnicodeDecodeError:
                    continue

            # إذا فشلت جميع الترميزات
            raise Exception("لا يمكن قراءة الملف بأي ترميز مدعوم")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف: {str(e)}")
            return None

    def process_srt_content(self, content):
        """معالجة محتوى ملف SRT"""
        if not content:
            return ""

        lines = content.split('\n')
        processed_lines = []

        for line in lines:
            original_line = line
            line = line.strip()

            # تخطي الأسطر الفارغة إذا كان الخيار مفعل
            if self.remove_empty_lines.get() and not line:
                continue
            elif not self.remove_empty_lines.get() and not line:
                processed_lines.append("")
                continue

            # حذف أرقام الترقيم (الأسطر التي تحتوي على أرقام فقط)
            if self.remove_numbers.get() and line.isdigit():
                continue

            # حذف التوقيتات (الأسطر التي تحتوي على --> )
            if self.remove_timing.get() and '-->' in line:
                continue

            # حذف كلمة "كورس"
            if self.remove_course.get():
                line = re.sub(r'\bكورس\b', '', line, flags=re.IGNORECASE)
                line = re.sub(r'\bcourse\b', '', line, flags=re.IGNORECASE)

            # تنظيف النص العربي
            if self.clean_arabic_text.get():
                line = self.clean_arabic_text_advanced(line)

            # حذف الكلمات الإنجليزية
            if self.remove_english_words.get():
                line = self.remove_english_from_text(line)

            # تنظيف المسافات الزائدة
            line = re.sub(r'\s+', ' ', line).strip()

            if line or not self.remove_empty_lines.get():  # إضافة السطر
                processed_lines.append(line)

        return '\n'.join(processed_lines)

    def clean_arabic_text_advanced(self, text):
        """تنظيف متقدم للنص العربي"""
        if not text:
            return text

        # إزالة التشكيل العربي
        arabic_diacritics = re.compile(r'[\u064B-\u0652\u0670\u0640]')
        text = arabic_diacritics.sub('', text)

        # إزالة الرموز الزائدة والأقواس
        text = re.sub(r'[()[\]{}]', '', text)
        text = re.sub(r'[«»""''‚„]', '', text)

        # إزالة علامات الترقيم الزائدة
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[,]{2,}', ',', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)

        # إزالة الأرقام الإنجليزية إذا كانت منفصلة
        text = re.sub(r'\b\d+\b', '', text)

        # تنظيف المسافات
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def remove_english_from_text(self, text):
        """حذف الكلمات الإنجليزية من النص"""
        if not text:
            return text

        # تقسيم النص إلى كلمات
        words = text.split()
        arabic_words = []

        for word in words:
            # إزالة علامات الترقيم من الكلمة للفحص
            clean_word = re.sub(r'[^\w\u0600-\u06FF]', '', word)

            # التحقق من وجود أحرف عربية في الكلمة
            if re.search(r'[\u0600-\u06FF]', clean_word):
                arabic_words.append(word)
            # إذا كانت الكلمة تحتوي على أرقام عربية أو رموز فقط
            elif re.search(r'[\u06F0-\u06F9\u0660-\u0669]', clean_word):
                arabic_words.append(word)
            # إذا كانت علامة ترقيم فقط
            elif len(clean_word) == 0 and len(word) > 0:
                arabic_words.append(word)

        return ' '.join(arabic_words)

    def process_file(self):
        """معالجة الملف المحدد"""
        if not self.input_file:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف SRT أولاً")
            return

        try:
            # قراءة الملف
            content = self.read_srt_file(self.input_file)
            if content is None:
                return

            # معالجة المحتوى
            self.processed_text = self.process_srt_content(content)

            # عرض النتيجة في منطقة المعاينة
            self.text_preview.delete(1.0, tk.END)
            self.text_preview.insert(1.0, self.processed_text)

            # إحصائيات
            lines_count = len([line for line in self.processed_text.split('\n') if line.strip()])
            chars_count = len(self.processed_text)

            messagebox.showinfo(
                "نجح",
                f"تم معالجة الملف بنجاح!\n\n"
                f"عدد الأسطر: {lines_count}\n"
                f"عدد الأحرف: {chars_count}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معالجة الملف: {str(e)}")

    def save_text(self):
        """حفظ النص المعالج"""
        if not self.processed_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى معالجة ملف أولاً")
            return

        # اختيار مكان حفظ الملف
        output_path = filedialog.asksaveasfilename(
            title="حفظ النص المعالج",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if not output_path:
            return

        try:
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(self.processed_text)

            messagebox.showinfo("نجح", f"تم حفظ الملف بنجاح!\nالمسار: {output_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def save_pdf(self):
        """حفظ النص المعالج كملف PDF"""
        if not PDF_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير مثبتة.\nيرجى تثبيتها أولاً: pip install reportlab")
            return

        if not self.processed_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى معالجة ملف أولاً")
            return

        # اختيار مكان حفظ ملف PDF
        output_path = filedialog.asksaveasfilename(
            title="حفظ ملف PDF",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )

        if not output_path:
            return

        try:
            # إنشاء ملف PDF
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=72
            )

            # إعداد الأنماط
            styles = getSampleStyleSheet()

            # محاولة تسجيل خط عربي
            arabic_font_name = 'Helvetica'
            try:
                # محاولة استخدام خط عربي إذا كان متوفراً
                arabic_font_name = self.register_arabic_font()
            except:
                pass

            # إنشاء نمط للنص العربي
            arabic_style = ParagraphStyle(
                'Arabic',
                parent=styles['Normal'],
                fontName=arabic_font_name,
                fontSize=16,  # خط أكبر للعربية
                leading=24,   # مسافة أكبر بين الأسطر
                alignment=TA_RIGHT,  # محاذاة لليمين
                spaceAfter=15,
                rightIndent=30,
                leftIndent=30,
                wordWrap='RTL'  # اتجاه النص من اليمين لليسار
            )

            # تقسيم النص إلى فقرات
            paragraphs = self.processed_text.split('\n')
            story = []

            # إضافة عنوان
            title_style = ParagraphStyle(
                'Title',
                parent=styles['Title'],
                fontName='Helvetica-Bold',
                fontSize=18,
                alignment=1,  # محاذاة في الوسط
                spaceAfter=20
            )

            story.append(Paragraph("النص المعالج من ملف SRT", title_style))
            story.append(Spacer(1, 20))

            for para in paragraphs:
                if para.strip():
                    p = Paragraph(para.strip(), arabic_style)
                    story.append(p)
                    story.append(Spacer(1, 8))

            # بناء المستند
            doc.build(story)

            messagebox.showinfo("نجح", f"تم إنشاء ملف PDF بنجاح!\nالمسار: {output_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء ملف PDF: {str(e)}")

    def register_arabic_font(self):
        """تسجيل خط عربي للاستخدام في PDF"""
        # قائمة بالخطوط العربية الشائعة في Windows
        arabic_fonts = [
            'C:/Windows/Fonts/arial.ttf',
            'C:/Windows/Fonts/tahoma.ttf',
            'C:/Windows/Fonts/calibri.ttf',
            'C:/Windows/Fonts/times.ttf',
            '/System/Library/Fonts/Arial.ttf',  # macOS
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
        ]

        for font_path in arabic_fonts:
            if os.path.exists(font_path):
                try:
                    font_name = os.path.basename(font_path).replace('.ttf', '')
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    return font_name
                except:
                    continue

        # إذا لم نجد خط مناسب، نستخدم Helvetica
        return 'Helvetica'

    def clear_text(self):
        """مسح النص"""
        self.text_preview.delete(1.0, tk.END)
        self.processed_text = ""
        self.file_path_var.set("")
        self.input_file = ""


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = SimpleSRTProcessor(root)
    root.mainloop()


if __name__ == "__main__":
    main()
