# دليل الميزات العربية المتقدمة

## 🎯 الميزات الجديدة للغة العربية

### 1. **تنظيف النص العربي المتقدم**
- ✅ **إزالة التشكيل**: حذف الفتحة والضمة والكسرة وجميع علامات التشكيل
- ✅ **إزالة الرموز الزائدة**: حذف الأقواس () [] {} والعلامات «»
- ✅ **تنظيف علامات الترقيم**: إزالة النقاط والفواصل المتكررة
- ✅ **حذف الأرقام الإنجليزية**: إزالة الأرقام المنفصلة (123, 456)

### 2. **فصل النصوص العربية والإنجليزية**
- ✅ **حذف الكلمات الإنجليزية**: إبقاء النص العربي فقط
- ✅ **الحفاظ على الأرقام العربية**: ٠١٢٣٤٥٦٧٨٩
- ✅ **الحفاظ على علامات الترقيم**: النقاط والفواصل المهمة

### 3. **تحسين PDF للعربية**
- ✅ **خط أكبر**: حجم 16 بدلاً من 14
- ✅ **مسافات أوسع**: بين الأسطر والفقرات
- ✅ **محاذاة يمين**: مناسبة للنص العربي
- ✅ **خطوط عربية**: استخدام خطوط النظام المناسبة

## 📋 خيارات المعالجة الجديدة

### الخيارات الأساسية:
- ☑️ حذف التوقيتات
- ☑️ حذف كلمة 'كورس'
- ☑️ حذف أرقام الترقيم
- ☑️ حذف الأسطر الفارغة

### الخيارات العربية المتقدمة:
- ☑️ **تنظيف النص العربي** (إزالة التشكيل والرموز الزائدة)
- ☐ **حذف الكلمات الإنجليزية** (اختياري)

## 🧪 مثال على المعالجة

### النص الأصلي:
```
1
00:00:00,500 --> 00:00:04,000
أهلاً وسهلاً بكم في كورس تعلُّم اللغة العربية

2
00:00:04,500 --> 00:00:08,000
سنتعلم اليوم قواعد النحو والصرف (Grammar Rules)

3
00:00:08,500 --> 00:00:12,000
نص بتشكيل كامل: الحَمْدُ لِلَّهِ رَبِّ العَالَمِينَ
```

### بعد التنظيف العربي:
```
أهلا وسهلا بكم في تعلم اللغة العربية
سنتعلم اليوم قواعد النحو والصرف
نص بتشكيل كامل: الحمد لله رب العالمين
```

### بعد حذف الإنجليزية أيضاً:
```
أهلا وسهلا بكم في تعلم اللغة العربية
سنتعلم اليوم قواعد النحو والصرف
نص بتشكيل كامل: الحمد لله رب العالمين
```

## 🎨 تحسينات PDF

### قبل التحسين:
- خط صغير (12-14)
- محاذاة عادية
- مسافات ضيقة

### بعد التحسين:
- خط أكبر (16)
- محاذاة يمين للعربية
- مسافات مريحة للقراءة
- خطوط نظام مناسبة

## 📁 ملفات الاختبار

1. **`test_arabic.srt`** - نص عربي معقد مع:
   - تشكيل كامل
   - كلمات إنجليزية
   - أرقام وأقواس
   - رموز متنوعة

2. **`sample.srt`** - نص مختلط عربي/إنجليزي

## 💡 نصائح للاستخدام

1. **للنصوص العربية الخالصة**: فعل جميع الخيارات
2. **للنصوص المختلطة**: اترك "حذف الإنجليزية" مغلق
3. **للنصوص المشكلة**: فعل "تنظيف النص العربي"
4. **للحصول على PDF جميل**: استخدم زر "حفظ PDF"

## 🔧 استكشاف الأخطاء

### إذا لم تظهر الخيارات الجديدة:
- تأكد من تشغيل `simple_srt_processor.py`
- أعد تشغيل البرنامج

### إذا لم يعمل حفظ PDF:
- ثبت reportlab: `pip install reportlab`
- استخدم "حفظ النص" كبديل

### إذا ظهر النص العربي معكوس في PDF:
- هذا طبيعي في بعض برامج عرض PDF
- جرب برنامج عرض مختلف
