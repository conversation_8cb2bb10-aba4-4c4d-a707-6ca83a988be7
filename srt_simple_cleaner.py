#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج ملفات SRT المبسط
يحذف فقط: التوقيت + الترقيم + كلمة "كورس"
ويحافظ على النص الأصلي كما هو
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re
import os


class SimpleSRTCleaner:
    def __init__(self, root):
        self.root = root
        self.root.title("منظف ملفات SRT - بسيط")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات
        self.input_file = ""
        self.cleaned_text = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.root, 
            text="منظف ملفات SRT", 
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2196F3'
        )
        title_label.pack(pady=20)
        
        # وصف البرنامج
        desc_label = tk.Label(
            self.root,
            text="يحذف: التوقيت + الترقيم + كلمة 'كورس' | يحافظ على: النص الأصلي كما هو",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#666666'
        )
        desc_label.pack(pady=(0, 20))
        
        # إطار اختيار الملف
        file_frame = tk.Frame(self.root, bg='#f0f0f0')
        file_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(file_frame, text="ملف SRT:", font=("Arial", 12, "bold"), bg='#f0f0f0').pack(anchor='w')
        
        file_select_frame = tk.Frame(file_frame, bg='#f0f0f0')
        file_select_frame.pack(fill='x', pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            file_select_frame, 
            textvariable=self.file_path_var,
            font=("Arial", 11),
            state='readonly'
        )
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(
            file_select_frame,
            text="اختر ملف",
            command=self.browse_file,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=5
        )
        browse_btn.pack(side='right')
        
        # منطقة معاينة النص
        preview_frame = tk.LabelFrame(
            self.root,
            text="النص المنظف",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        preview_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # منطقة النص مع scrollbar
        self.text_preview = scrolledtext.ScrolledText(
            preview_frame,
            height=15,
            font=("Arial", 15),  # خط كبير للقراءة
            wrap='word',
            bg='white',
            fg='#333333'
        )
        self.text_preview.pack(fill='both', expand=True, padx=10, pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(pady=20, padx=20, fill='x')
        
        # زر التنظيف
        clean_btn = tk.Button(
            buttons_frame,
            text="تنظيف الملف",
            command=self.clean_file,
            bg='#2196F3',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=40,
            pady=12
        )
        clean_btn.pack(side='left', padx=(0, 15))
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ النص",
            command=self.save_text,
            bg='#FF9800',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=40,
            pady=12
        )
        save_btn.pack(side='left', padx=(0, 15))
        
        # زر المسح
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح",
            command=self.clear_all,
            bg='#f44336',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=40,
            pady=12
        )
        clear_btn.pack(side='left')
        
    def browse_file(self):
        """تصفح واختيار ملف SRT"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف SRT",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            self.input_file = file_path
            self.file_path_var.set(file_path)
            
    def read_file(self, file_path):
        """قراءة ملف SRT"""
        try:
            # محاولة قراءة الملف بترميزات مختلفة
            encodings = ['utf-8', 'utf-8-sig', 'cp1256', 'windows-1256', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        return content
                except UnicodeDecodeError:
                    continue
                    
            # إذا فشلت جميع الترميزات
            raise Exception("لا يمكن قراءة الملف بأي ترميز مدعوم")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف: {str(e)}")
            return None
            
    def clean_srt_content(self, content):
        """تنظيف محتوى ملف SRT - بسيط جداً"""
        if not content:
            return ""
            
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # تخطي الأسطر الفارغة
            if not line:
                continue
                
            # حذف أرقام الترقيم (الأسطر التي تحتوي على أرقام فقط)
            if line.isdigit():
                continue
                
            # حذف التوقيتات (الأسطر التي تحتوي على --> )
            if '-->' in line:
                continue
                
            # حذف كلمة "كورس" بالعربية والإنجليزية
            line = re.sub(r'\bكورس\b', '', line, flags=re.IGNORECASE)
            line = re.sub(r'\bcourse\b', '', line, flags=re.IGNORECASE)
                
            # تنظيف المسافات الزائدة فقط
            line = re.sub(r'\s+', ' ', line).strip()
            
            # إضافة السطر إذا لم يكن فارغاً
            if line:
                cleaned_lines.append(line)
                
        return '\n'.join(cleaned_lines)
        
    def clean_file(self):
        """تنظيف الملف المحدد"""
        if not self.input_file:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف SRT أولاً")
            return
            
        try:
            # قراءة الملف
            content = self.read_file(self.input_file)
            if content is None:
                return
                
            # تنظيف المحتوى
            self.cleaned_text = self.clean_srt_content(content)
            
            # عرض النتيجة في منطقة المعاينة
            self.text_preview.delete(1.0, tk.END)
            self.text_preview.insert(1.0, self.cleaned_text)
            
            # إحصائيات
            lines_count = len([line for line in self.cleaned_text.split('\n') if line.strip()])
            chars_count = len(self.cleaned_text)
            words_count = len(self.cleaned_text.split())
            
            messagebox.showinfo(
                "تم التنظيف بنجاح!", 
                f"النتائج:\n\n"
                f"عدد الأسطر: {lines_count}\n"
                f"عدد الكلمات: {words_count}\n"
                f"عدد الأحرف: {chars_count}"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنظيف الملف: {str(e)}")
            
    def save_text(self):
        """حفظ النص المنظف"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return
            
        # اختيار مكان حفظ الملف
        output_path = filedialog.asksaveasfilename(
            title="حفظ النص المنظف",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if not output_path:
            return
            
        try:
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(self.cleaned_text)
                
            messagebox.showinfo("تم الحفظ!", f"تم حفظ الملف بنجاح:\n{output_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")
            
    def clear_all(self):
        """مسح كل شيء"""
        self.text_preview.delete(1.0, tk.END)
        self.cleaned_text = ""
        self.file_path_var.set("")
        self.input_file = ""


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = SimpleSRTCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()
