#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج ملفات SRT المبسط
يحذف فقط: التوقيت + الترقيم + كلمة "كورس"
ويحافظ على النص الأصلي كما هو
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re
import os

# محاولة استيراد مكتبة PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False


class SimpleSRTCleaner:
    def __init__(self, root):
        self.root = root
        self.root.title("منظف ملفات SRT - بسيط")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')

        # متغيرات
        self.input_file = ""
        self.cleaned_text = ""

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.root,
            text="منظف ملفات SRT",
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2196F3'
        )
        title_label.pack(pady=20)

        # وصف البرنامج
        desc_label = tk.Label(
            self.root,
            text="يحذف: التوقيت + الترقيم + كلمة 'كورس' | يحافظ على: النص الأصلي كما هو",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#666666'
        )
        desc_label.pack(pady=(0, 20))

        # إطار اختيار الملف
        file_frame = tk.Frame(self.root, bg='#f0f0f0')
        file_frame.pack(pady=10, padx=20, fill='x')

        tk.Label(file_frame, text="ملف SRT:", font=("Arial", 12, "bold"), bg='#f0f0f0').pack(anchor='w')

        file_select_frame = tk.Frame(file_frame, bg='#f0f0f0')
        file_select_frame.pack(fill='x', pady=5)

        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            file_select_frame,
            textvariable=self.file_path_var,
            font=("Arial", 11),
            state='readonly'
        )
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = tk.Button(
            file_select_frame,
            text="اختر ملف",
            command=self.browse_file,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 11, "bold"),
            padx=20,
            pady=5
        )
        browse_btn.pack(side='right')

        # منطقة معاينة النص
        preview_frame = tk.LabelFrame(
            self.root,
            text="النص المنظف",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        preview_frame.pack(pady=20, padx=20, fill='both', expand=True)

        # منطقة النص مع scrollbar
        self.text_preview = scrolledtext.ScrolledText(
            preview_frame,
            height=15,
            font=("Arial", 15),  # خط كبير للقراءة
            wrap='word',
            bg='white',
            fg='#333333'
        )
        self.text_preview.pack(fill='both', expand=True, padx=10, pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(pady=20, padx=20, fill='x')

        # زر التنظيف
        clean_btn = tk.Button(
            buttons_frame,
            text="تنظيف الملف",
            command=self.clean_file,
            bg='#2196F3',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=40,
            pady=12
        )
        clean_btn.pack(side='left', padx=(0, 15))

        # زر حفظ النص
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ نص",
            command=self.save_text,
            bg='#FF9800',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=30,
            pady=12
        )
        save_btn.pack(side='left', padx=(0, 10))

        # زر حفظ PDF
        if PDF_AVAILABLE:
            pdf_btn = tk.Button(
                buttons_frame,
                text="حفظ PDF",
                command=self.save_pdf,
                bg='#9C27B0',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=30,
                pady=12
            )
            pdf_btn.pack(side='left', padx=(0, 10))
        else:
            # زر تثبيت PDF إذا لم تكن المكتبة متوفرة
            install_btn = tk.Button(
                buttons_frame,
                text="تثبيت PDF",
                command=self.install_pdf_library,
                bg='#795548',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=30,
                pady=12
            )
            install_btn.pack(side='left', padx=(0, 10))

        # زر PDF بديل (بدون مكتبات خارجية)
        simple_pdf_btn = tk.Button(
            buttons_frame,
            text="PDF بسيط",
            command=self.save_simple_pdf,
            bg='#607D8B',
            fg='white',
            font=("Arial", 10),
            padx=20,
            pady=12
        )
        simple_pdf_btn.pack(side='left', padx=(0, 10))

        # زر المسح
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح",
            command=self.clear_all,
            bg='#f44336',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=40,
            pady=12
        )
        clear_btn.pack(side='left')

    def browse_file(self):
        """تصفح واختيار ملف SRT"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف SRT",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            self.input_file = file_path
            self.file_path_var.set(file_path)

    def read_file(self, file_path):
        """قراءة ملف SRT"""
        try:
            # محاولة قراءة الملف بترميزات مختلفة
            encodings = ['utf-8', 'utf-8-sig', 'cp1256', 'windows-1256', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        return content
                except UnicodeDecodeError:
                    continue

            # إذا فشلت جميع الترميزات
            raise Exception("لا يمكن قراءة الملف بأي ترميز مدعوم")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف: {str(e)}")
            return None

    def clean_srt_content(self, content):
        """تنظيف محتوى ملف SRT - بسيط جداً"""
        if not content:
            return ""

        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # تخطي الأسطر الفارغة
            if not line:
                continue

            # حذف أرقام الترقيم (الأسطر التي تحتوي على أرقام فقط)
            if line.isdigit():
                continue

            # حذف التوقيتات (الأسطر التي تحتوي على --> )
            if '-->' in line:
                continue

            # حذف كلمة "كورس" بالعربية والإنجليزية
            line = re.sub(r'\bكورس\b', '', line, flags=re.IGNORECASE)
            line = re.sub(r'\bcourse\b', '', line, flags=re.IGNORECASE)

            # تنظيف المسافات الزائدة فقط
            line = re.sub(r'\s+', ' ', line).strip()

            # إضافة السطر إذا لم يكن فارغاً
            if line:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def clean_file(self):
        """تنظيف الملف المحدد"""
        if not self.input_file:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف SRT أولاً")
            return

        try:
            # قراءة الملف
            content = self.read_file(self.input_file)
            if content is None:
                return

            # تنظيف المحتوى
            self.cleaned_text = self.clean_srt_content(content)

            # عرض النتيجة في منطقة المعاينة
            self.text_preview.delete(1.0, tk.END)
            self.text_preview.insert(1.0, self.cleaned_text)

            # إحصائيات
            lines_count = len([line for line in self.cleaned_text.split('\n') if line.strip()])
            chars_count = len(self.cleaned_text)
            words_count = len(self.cleaned_text.split())

            messagebox.showinfo(
                "تم التنظيف بنجاح!",
                f"النتائج:\n\n"
                f"عدد الأسطر: {lines_count}\n"
                f"عدد الكلمات: {words_count}\n"
                f"عدد الأحرف: {chars_count}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنظيف الملف: {str(e)}")

    def save_text(self):
        """حفظ النص المنظف"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        # اختيار مكان حفظ الملف
        output_path = filedialog.asksaveasfilename(
            title="حفظ النص المنظف",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if not output_path:
            return

        try:
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(self.cleaned_text)

            messagebox.showinfo("تم الحفظ!", f"تم حفظ الملف بنجاح:\n{output_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")

    def save_pdf(self):
        """حفظ النص كملف PDF - نسخة مبسطة جداً"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        # اختيار مكان حفظ ملف PDF
        output_path = filedialog.asksaveasfilename(
            title="حفظ ملف PDF",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )

        if not output_path:
            return

        try:
            if PDF_AVAILABLE:
                # محاولة استخدام reportlab
                from reportlab.pdfgen import canvas
                from reportlab.lib.pagesizes import A4

                c = canvas.Canvas(output_path, pagesize=A4)
                width, height = A4

                # إعدادات بسيطة
                y = height - 100

                # العنوان
                c.setFont("Helvetica-Bold", 16)
                c.drawString(100, y, "SRT Cleaned Text")
                y -= 40

                # النص
                c.setFont("Helvetica", 12)
                lines = self.cleaned_text.split('\n')

                for line in lines:
                    if line.strip():
                        if y < 100:  # صفحة جديدة
                            c.showPage()
                            y = height - 100
                            c.setFont("Helvetica", 12)

                        # تقسيم الأسطر الطويلة
                        if len(line) > 80:
                            words = line.split()
                            current_line = ""
                            for word in words:
                                if len(current_line + " " + word) < 80:
                                    current_line += " " + word if current_line else word
                                else:
                                    if current_line:
                                        c.drawString(50, y, current_line)
                                        y -= 20
                                    current_line = word
                            if current_line:
                                c.drawString(50, y, current_line)
                                y -= 20
                        else:
                            c.drawString(50, y, line.strip())
                            y -= 20

                c.save()
                messagebox.showinfo("نجح!", f"تم حفظ PDF:\n{output_path}")

            else:
                # إذا لم تكن reportlab متوفرة، استخدم HTML
                self.save_simple_pdf()

        except Exception as e:
            messagebox.showerror("خطأ في PDF", f"فشل في إنشاء PDF:\n{str(e)}\n\nسيتم استخدام HTML بدلاً منه...")
            # استخدام HTML كبديل
            self.save_simple_pdf()

    def install_pdf_library(self):
        """تثبيت مكتبة reportlab"""
        import subprocess
        import sys

        result = messagebox.askyesno(
            "تثبيت مكتبة PDF",
            "هل تريد تثبيت مكتبة reportlab لحفظ ملفات PDF؟\n\nسيتم تشغيل الأمر:\npip install reportlab"
        )

        if result:
            try:
                messagebox.showinfo("جاري التثبيت", "جاري تثبيت المكتبة...\nقد يستغرق دقيقة أو دقيقتين")

                # تشغيل أمر التثبيت
                subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])

                messagebox.showinfo(
                    "تم التثبيت!",
                    "تم تثبيت مكتبة reportlab بنجاح!\n\nيرجى إعادة تشغيل البرنامج لتفعيل زر 'حفظ PDF'"
                )

            except subprocess.CalledProcessError:
                messagebox.showerror(
                    "فشل التثبيت",
                    "فشل في تثبيت المكتبة.\n\nيمكنك تثبيتها يدوياً من Command Prompt:\npip install reportlab"
                )
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في التثبيت: {str(e)}")

    def save_simple_pdf(self):
        """حفظ النص كملف HTML (يمكن طباعته كـ PDF)"""
        if not self.cleaned_text:
            messagebox.showwarning("تحذير", "لا يوجد نص لحفظه. يرجى تنظيف ملف أولاً")
            return

        # اختيار مكان حفظ ملف HTML
        output_path = filedialog.asksaveasfilename(
            title="حفظ ملف HTML (للطباعة كـ PDF)",
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")]
        )

        if not output_path:
            return

        try:
            # إنشاء محتوى HTML منسق
            html_content = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النص المنظف من ملف SRT</title>
    <style>
        body {{
            font-family: 'Arial', 'Tahoma', sans-serif;
            font-size: 16px;
            line-height: 1.8;
            margin: 40px;
            direction: rtl;
            text-align: right;
            background-color: white;
            color: #333;
        }}

        h1 {{
            text-align: center;
            color: #2196F3;
            font-size: 24px;
            margin-bottom: 30px;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }}

        .content {{
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }}

        p {{
            margin-bottom: 15px;
            text-align: right;
        }}

        @media print {{
            body {{
                margin: 20px;
            }}

            .no-print {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="content">
        <h1>النص المنظف من ملف SRT</h1>

        <div class="no-print" style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
            <strong>تعليمات:</strong> لحفظ هذا الملف كـ PDF، اضغط Ctrl+P واختر "حفظ كـ PDF"
        </div>

"""

            # إضافة النص
            lines = self.cleaned_text.split('\n')
            for line in lines:
                if line.strip():
                    html_content += f"        <p>{line.strip()}</p>\n"

            html_content += """    </div>
</body>
</html>"""

            # حفظ الملف
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # عرض رسالة نجاح مع تعليمات
            result = messagebox.askyesno(
                "تم الحفظ!",
                f"تم حفظ ملف HTML بنجاح:\n{output_path}\n\n"
                "هل تريد فتح الملف الآن؟\n\n"
                "تعليمات: لتحويله إلى PDF، افتح الملف في المتصفح واضغط Ctrl+P ثم اختر 'حفظ كـ PDF'"
            )

            if result:
                # فتح الملف في المتصفح الافتراضي
                import webbrowser
                webbrowser.open(output_path)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء ملف HTML: {str(e)}")

    def clear_all(self):
        """مسح كل شيء"""
        self.text_preview.delete(1.0, tk.END)
        self.cleaned_text = ""
        self.file_path_var.set("")
        self.input_file = ""


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = SimpleSRTCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()
