@echo off
echo ========================================
echo     تحويل منظف SRT إلى ملف تنفيذي
echo ========================================
echo.

echo 📦 تثبيت PyInstaller...
pip install pyinstaller

echo.
echo 🔨 تحويل البرنامج إلى ملف تنفيذي...
pyinstaller --onefile --windowed --name "منظف_SRT" --icon=icon.ico srt_cleaner_final.py

echo.
echo ✅ تم التحويل!
echo 📁 ستجد الملف التنفيذي في مجلد: dist
echo 📄 اسم الملف: منظف_SRT.exe

echo.
echo 💡 يمكنك الآن:
echo    1. نسخ الملف من مجلد dist
echo    2. وضعه في أي مكان
echo    3. تشغيله مباشرة بدون Python

pause
