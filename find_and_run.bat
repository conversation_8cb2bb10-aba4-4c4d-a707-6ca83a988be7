@echo off
cd /d "%~dp0"
echo Finding Python installation...

REM Check if py launcher works
py --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python launcher, starting program...
    py srt_cleaner_final.py
    goto end
)

REM Check common Python installation paths
for %%i in (
    "C:\Python312\python.exe"
    "C:\Python311\python.exe" 
    "C:\Python310\python.exe"
    "C:\Python39\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe"
) do (
    if exist %%i (
        echo Found Python at %%i
        %%i srt_cleaner_final.py
        goto end
    )
)

echo Python not found in common locations.
echo.
echo Please:
echo 1. Go to Settings > Apps > Advanced app settings > App execution aliases
echo 2. Turn OFF "Python" and "Python3" 
echo 3. Then try again
echo.
echo Or install Python from python.org with "Add to PATH" checked

:end
pause
