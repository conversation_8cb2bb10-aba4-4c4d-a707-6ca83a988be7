@echo off
title منظف ملفات SRT
cd /d "%~dp0"

echo ========================================
echo        🧹 منظف ملفات SRT 🧹
echo ========================================
echo.
echo ✅ الوظائف:
echo    • حذف التوقيت والترقيم
echo    • حذف كلمة "كورس"
echo    • الحفاظ على التنسيق الأصلي
echo    • محاذاة تلقائية حسب اللغة
echo    • حفظ تلقائي باسم الملف + _2
echo.

echo 🚀 تشغيل البرنامج...
python srt_cleaner_final.py

if errorlevel 1 (
    echo.
    echo ❌ خطأ: لم يتم العثور على Python
    echo.
    echo 💡 الحلول:
    echo    1. تثبيت Python من: https://www.python.org/downloads/
    echo    2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo    3. أعد تشغيل الكمبيوتر بعد التثبيت
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح
pause
