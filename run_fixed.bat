@echo off
cd /d "%~dp0"
echo Starting SRT Cleaner...

REM Try different Python commands
py srt_cleaner_final.py
if not errorlevel 1 goto success

python3 srt_cleaner_final.py
if not errorlevel 1 goto success

python.exe srt_cleaner_final.py
if not errorlevel 1 goto success

C:\Python312\python.exe srt_cleaner_final.py
if not errorlevel 1 goto success

C:\Python311\python.exe srt_cleaner_final.py
if not errorlevel 1 goto success

C:\Python310\python.exe srt_cleaner_final.py
if not errorlevel 1 goto success

echo.
echo Python not found. Trying to find Python installation...
where python
where py
where python3

echo.
echo Solutions:
echo 1. Disable Microsoft Store Python alias:
echo    Settings > Apps > Advanced app settings > App execution aliases
echo    Turn OFF Python and Python3
echo.
echo 2. Or install Python properly from python.org
echo    Make sure to check "Add Python to PATH"
echo.
pause
exit /b 1

:success
echo Program finished successfully
pause
