@echo off
chcp 65001 >nul
title SRT Cleaner
cd /d "%~dp0"

echo ========================================
echo           SRT File Cleaner
echo ========================================
echo.
echo Functions:
echo   - Remove timing and numbering
echo   - Remove word "course"
echo   - Keep original formatting
echo   - Auto alignment by language
echo   - Auto save with filename + _2
echo.

echo Starting program...
python srt_cleaner_final.py

if errorlevel 1 (
    echo.
    echo ERROR: Python not found
    echo.
    echo Solutions:
    echo   1. Install Python from: https://www.python.org/downloads/
    echo   2. Make sure to check "Add Python to PATH" during installation
    echo   3. Restart computer after installation
    echo.
    pause
    exit /b 1
)

echo.
echo Program closed successfully
pause
