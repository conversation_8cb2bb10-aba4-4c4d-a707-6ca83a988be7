# 🚀 دليل تشغيل منظف SRT

## 📁 ملفات التشغيل المتوفرة

### 1. **تشغيل_منظف_SRT.bat** (الأفضل)
- **تشغيل كامل** مع رسائل واضحة
- **فحص Python** وعرض رسائل مفيدة
- **تعليمات واضحة** في حالة وجود مشاكل

### 2. **تثبيت_وتشغيل.bat** (للمبتدئين)
- **فحص Python** تلقائياً
- **فتح موقع التحميل** إذا لم يكن مثبت
- **تعليمات التثبيت** واضحة

### 3. **تشغيل_سريع.bat** (للخبراء)
- **تشغيل مباشر** بدون رسائل
- **الأسرع** للاستخدام المتكرر

## 🎯 طريقة الاستخدام

### للمرة الأولى:
1. **انقر نقراً مزدوجاً** على `تثبيت_وتشغيل.bat`
2. **إذا طلب تثبيت Python** - اتبع التعليمات
3. **بعد التثبيت** - أعد تشغيل الملف

### للاستخدام العادي:
1. **انقر نقراً مزدوجاً** على `تشغيل_منظف_SRT.bat`
2. **سيفتح البرنامج** مباشرة

### للاستخدام السريع:
1. **انقر نقراً مزدوجاً** على `تشغيل_سريع.bat`
2. **تشغيل فوري** بدون انتظار

## 🔧 متطلبات التشغيل

### Python:
- **يجب تثبيت Python** على الجهاز
- **تحميل من**: https://www.python.org/downloads/
- **مهم**: تحديد "Add Python to PATH" أثناء التثبيت

### الملفات المطلوبة:
- `srt_cleaner_final.py` - البرنامج الرئيسي
- أحد ملفات .bat للتشغيل

## 📋 خطوات التشغيل

### 1. التحضير:
- **ضع ملفات SRT** في أي مجلد
- **تأكد من وجود** ملف .bat في نفس مجلد البرنامج

### 2. التشغيل:
- **انقر مزدوج** على ملف .bat
- **انتظر فتح** نافذة البرنامج

### 3. الاستخدام:
- **اختر ملف SRT** - 📂 تصفح
- **نظف الملف** - 🧹 تنظيف
- **احفظ تلقائياً** - 💾 حفظ تلقائي

## 🛠️ حل المشاكل

### إذا لم يعمل البرنامج:
1. **تأكد من تثبيت Python**
2. **أعد تشغيل الكمبيوتر** بعد تثبيت Python
3. **جرب ملف bat مختلف**

### إذا ظهرت رسالة خطأ:
1. **اقرأ الرسالة** في نافذة الأوامر
2. **اتبع التعليمات** المعروضة
3. **استخدم ملف "تثبيت_وتشغيل.bat"**

### إذا أغلقت النافذة بسرعة:
1. **استخدم "تشغيل_منظف_SRT.bat"**
2. **ستبقى النافذة مفتوحة** لقراءة الرسائل

## 💡 نصائح

### للاستخدام الأمثل:
- **ضع ملف .bat** على سطح المكتب للوصول السريع
- **استخدم "تشغيل_سريع.bat"** للاستخدام المتكرر
- **احتفظ بنسخة احتياطية** من الملفات

### لتوزيع البرنامج:
- **انسخ المجلد كاملاً** لأي جهاز آخر
- **تأكد من وجود Python** على الجهاز الآخر
- **شارك ملف "تثبيت_وتشغيل.bat"** مع المستخدمين الجدد

## 🎉 النتيجة

**تشغيل سهل ومباشر للبرنامج بنقرة واحدة!**

**اختر الملف المناسب لك:**
- 🔰 **مبتدئ**: `تثبيت_وتشغيل.bat`
- 👤 **عادي**: `تشغيل_منظف_SRT.bat`  
- ⚡ **سريع**: `تشغيل_سريع.bat`
