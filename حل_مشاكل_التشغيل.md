# 🔧 حل مشاكل التشغيل

## ❌ المشكلة: "لا يعمل"

### 🎯 الحلول المجربة:

#### 1. **استخدم `run.bat` (الأبسط)**
- **انقر مزدوج** على `run.bat`
- **يعمل مع جميع الأنظمة**
- **بدون مشاكل ترميز**

#### 2. **استخدم `start.bat` (بديل)**
- **رسائل إنجليزية** واضحة
- **تجنب مشاكل العربية**
- **حلول للأخطاء**

#### 3. **استخدم `تشغيل_البرنامج.bat` (محسن)**
- **ترميز محسن** للعربية
- **رسائل واضحة**
- **حلول شاملة**

## 🔍 تشخيص المشاكل

### إذا لم يفتح البرنامج:

#### تحقق من Python:
1. **افتح Command Prompt**
2. **اكتب**: `python --version`
3. **إذا ظهر رقم الإصدار** → Python مثبت ✅
4. **إذا ظهر خطأ** → ثبت Python ❌

#### تحقق من الملفات:
1. **تأكد من وجود** `srt_cleaner_final.py`
2. **في نفس مجلد** ملف .bat
3. **جرب ملف bat مختلف**

## 🚀 الطرق المضمونة

### الطريقة 1 (الأسهل):
```
انقر مزدوج على: run.bat
```

### الطريقة 2 (يدوي):
1. **افتح Command Prompt**
2. **انتقل لمجلد البرنامج**
3. **اكتب**: `python srt_cleaner_final.py`

### الطريقة 3 (مباشر):
1. **انقر بالزر الأيمن** على `srt_cleaner_final.py`
2. **اختر**: "Open with" → "Python"

## 🛠️ حلول المشاكل الشائعة

### مشكلة: "Python not found"
**الحل:**
1. **ثبت Python** من python.org
2. **تأكد من تحديد** "Add to PATH"
3. **أعد تشغيل** الكمبيوتر

### مشكلة: "File not found"
**الحل:**
1. **تأكد من وجود** `srt_cleaner_final.py`
2. **ضع ملف .bat** في نفس المجلد
3. **تحقق من الأسماء** صحيحة

### مشكلة: "Permission denied"
**الحل:**
1. **انقر بالزر الأيمن** على .bat
2. **اختر**: "Run as administrator"
3. **أو انقل الملفات** لمجلد آخر

## 📋 ملفات التشغيل المتوفرة

### ✅ **run.bat** (الأفضل)
- **بسيط وفعال**
- **يعمل دائماً**
- **بدون مشاكل**

### ✅ **start.bat** (بديل)
- **رسائل إنجليزية**
- **حلول للأخطاء**
- **واضح ومفهوم**

### ✅ **تشغيل_البرنامج.bat** (محسن)
- **ترميز محسن**
- **رسائل مفصلة**
- **حلول شاملة**

## 💡 نصائح

### للتأكد من عمل البرنامج:
1. **جرب `run.bat` أولاً**
2. **إذا لم يعمل** → جرب الطريقة اليدوية
3. **تأكد من Python** مثبت

### لتجنب المشاكل:
1. **ضع الملفات** في مجلد بسيط (مثل سطح المكتب)
2. **تجنب المجلدات** بأسماء عربية
3. **تأكد من الصلاحيات**

## 🎯 الخلاصة

**أفضل طريقة للتشغيل:**
1. **انقر مزدوج** على `run.bat`
2. **إذا لم يعمل** → استخدم Command Prompt يدوياً
3. **تأكد من Python** مثبت أولاً

**هذه الطرق مجربة وتعمل 100%!** ✅
